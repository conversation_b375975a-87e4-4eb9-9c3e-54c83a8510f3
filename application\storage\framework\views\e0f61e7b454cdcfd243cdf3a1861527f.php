<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <!-- Create Post Form - Large Container -->
    <div class="w-full max-w-screen-xl mx-auto px-4 sm:px-6 lg:px-8 mb-10">
        <form action="<?php echo e(route('posts.store')); ?>" method="POST" enctype="multipart/form-data" class="space-y-6">
            <?php echo csrf_field(); ?>

            <!-- Zone de saisie -->
            <div class="bg-white dark:bg-indigo-800 p-4 rounded-xl shadow-md border border-gray-200 dark:border-indigo-700">
                <textarea name="content" rows="4"
                    class="w-full bg-white dark:bg-indigo-900 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-300 border border-gray-300 dark:border-indigo-700 rounded-md shadow-sm
                           focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:focus:ring-indigo-400 focus:border-indigo-500 dark:focus:border-indigo-400 resize-none"
                    placeholder="Quoi de neuf ?"></textarea>

                 <!-- Prévisualisation des médias -->
             <div id="media-preview" class="mt-4">
                <div id="image-preview" class="hidden  p-4 rounded-sm shadow-md ">
                    <img src="" alt="Prévisualisation" class="max-h-48 rounded-lg">
                    <button type="button" onclick="removeMedia('image')" class="mt-2 text-red-600 hover:text-red-900">
                        Supprimer l'image
                    </button>
                </div>
                <div id="video-preview" class="hidden bg-white p-4 rounded-sm shadow-md border border-gray-200">
                    <video controls class="max-h-48 rounded-lg">
                        <source src="" type="video/mp4">
                        Votre navigateur ne supporte pas la lecture de vidéos.
                    </video>
                    <button aria-label="Supprimer la vidéo" type="button" onclick="removeMedia('video')" class="mt-2 text-red-600 hover:text-red-900">
                        Supprimer la vidéo
                    </button>
                </div>
            </div>
            </div>




            <!-- Zone de sélection fichiers -->
            <div class=" p-4 rounded-sm  ">
                <div class="flex items-center justify-between">
                    <div class="flex space-x-4">
                        <label class="cursor-pointer">
                            <input type="file" name="image" class="hidden" accept="image/*" onchange="previewImage(this)">
                            <span class="flex items-center space-x-2 text-gray-600 hover:text-gray-900">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z">
                                    </path>
                                </svg>
                                <span>Photo</span>
                            </span>
                        </label>
                        <label class="cursor-pointer">
                            <input type="file" name="video" class="hidden" accept="video/*" onchange="previewVideo(this)">
                            <span class="flex items-center space-x-2 text-gray-600 hover:text-gray-900">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z">
                                    </path>
                                </svg>
                                <span>Vidéo</span>
                            </span>
                        </label>
                    </div>

                    <button type="submit"
                        class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        Publier
                    </button>
                </div>
            </div>


        </form>
    </div>


<!-- Mobile Notifications Button -->
<div class="lg:hidden mb-4 px-4 sm:px-6">
    <button onclick="toggleMobileNotifications()"
            class="w-full bg-white dark:bg-gray-800 shadow-lg rounded-xl p-4 flex items-center justify-between">
        <div class="flex items-center space-x-2">
            <svg class="w-5 h-5 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5z"></path>
            </svg>
            <span class="font-medium text-gray-900 dark:text-gray-100">Notifications</span>
            <?php if(isset($notifications) && $notifications->where('is_read', false)->count() > 0): ?>
                <span class="bg-red-500 text-white text-xs rounded-full px-2 py-1">
                    <?php echo e($notifications->where('is_read', false)->count()); ?>

                </span>
            <?php endif; ?>
        </div>
        <svg class="w-5 h-5 text-gray-400 transform transition-transform" id="mobile-notifications-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
        </svg>
    </button>

    <!-- Mobile Notifications Panel -->
    <div id="mobile-notifications" class="hidden mt-2 bg-white dark:bg-gray-800 shadow-lg rounded-xl p-4 max-h-96 overflow-y-auto">
        <?php if(isset($notifications) && $notifications->count() > 0): ?>
            <div class="space-y-2">
                <?php $__currentLoopData = $notifications->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $notification): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="p-2 rounded bg-gray-50 dark:bg-gray-700">
                        <p class="text-xs text-gray-600 dark:text-gray-300"><?php echo e($notification->message); ?></p>
                        <div class="mt-1">
                            <?php if($notification->type === 'follow'): ?>
                                <a href="<?php echo e(route('profile.show', $notification->fromUser)); ?>"
                                   class="text-xs text-indigo-600 hover:text-indigo-800 dark:text-indigo-400">
                                    Voir le profil
                                </a>
                            <?php elseif(in_array($notification->type, ['like', 'comment', 'new_post']) && isset($notification->data['post_id'])): ?>
                                <a href="<?php echo e(route('posts.show', $notification->data['post_id'])); ?>"
                                   class="text-xs text-indigo-600 hover:text-indigo-800 dark:text-indigo-400">
                                    Voir le post
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
            <div class="mt-3 text-center">
                <a href="<?php echo e(route('notifications.index')); ?>"
                   class="text-sm text-indigo-600 hover:text-indigo-800 dark:text-indigo-400">
                    Voir toutes les notifications →
                </a>
            </div>
        <?php else: ?>
            <p class="text-sm text-gray-500 text-center">Aucune notification</p>
        <?php endif; ?>
    </div>
</div>

<!-- Main Content with Sidebar Layout -->
<div class="max-w-screen-xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex gap-8">
        <!-- Notifications Sidebar - Left Side -->
        <div class="hidden lg:block w-80 flex-shrink-0">
            <div class="bg-white dark:bg-gray-800 shadow-lg rounded-xl p-6 sticky top-20 notifications-sidebar" style="height: calc(100vh - 6rem);">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">Notifications</h3>

                <!-- Notifications Section -->
                <div class="mt-4">
                    <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Activité récente</h4>
                    <?php if(isset($notifications) && $notifications->count() > 0): ?>
                        <div class="space-y-2 max-h-64 overflow-y-auto">
                            <?php $__currentLoopData = $notifications->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $notification): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="p-2 rounded bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 transition">
                                    <p class="text-xs text-gray-600 dark:text-gray-300"><?php echo e($notification->message); ?></p>
                                    <div class="mt-1">
                                        <?php if($notification->type === 'follow'): ?>
                                            <a href="<?php echo e(route('profile.show', $notification->fromUser)); ?>"
                                               class="text-xs text-indigo-600 hover:text-indigo-800 dark:text-indigo-400">
                                                Voir le profil
                                            </a>
                                        <?php elseif(in_array($notification->type, ['like', 'comment', 'new_post']) && isset($notification->data['post_id'])): ?>
                                            <a href="<?php echo e(route('posts.show', $notification->data['post_id'])); ?>"
                                               class="text-xs text-indigo-600 hover:text-indigo-800 dark:text-indigo-400">
                                                Voir le post
                                            </a>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                        <div class="mt-3">
                            <a href="<?php echo e(route('notifications.index')); ?>"
                               class="text-xs text-indigo-600 hover:text-indigo-800 dark:text-indigo-400">
                                Voir toutes les notifications →
                            </a>
                        </div>
                    <?php else: ?>
                        <p class="text-sm text-gray-500">Aucune notification</p>
                    <?php endif; ?>
                </div>

                <!-- Suggestions Section -->
                <div class="mt-6">
                    <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Suggestions d'amis</h4>
                    <?php if(isset($friendSuggestions) && $friendSuggestions->count() > 0): ?>
                        <div class="space-y-2 max-h-48 overflow-y-auto">
                            <?php $__currentLoopData = $friendSuggestions->take(4); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $suggestion): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="flex items-center justify-between p-2 rounded bg-gray-50 dark:bg-gray-700">
                                    <div class="flex items-center space-x-2 min-w-0">
                                        <?php if($suggestion->profile_image): ?>
                                            <img class="h-6 w-6 rounded-full object-cover flex-shrink-0"
                                                 src="<?php echo e(Storage::url($suggestion->profile_image)); ?>"
                                                 alt="<?php echo e($suggestion->name); ?>">
                                        <?php else: ?>
                                            <img class="h-6 w-6 rounded-full flex-shrink-0"
                                                 src="https://ui-avatars.com/api/?name=<?php echo e(urlencode($suggestion->name)); ?>&size=24"
                                                 alt="<?php echo e($suggestion->name); ?>">
                                        <?php endif; ?>
                                        <span class="text-sm text-gray-600 dark:text-gray-300 truncate"><?php echo e($suggestion->name); ?></span>
                                    </div>
                                    <form action="<?php echo e(route('users.follow', $suggestion)); ?>" method="POST" class="inline">
                                        <?php echo csrf_field(); ?>
                                        <button type="submit" class="px-2 py-1 text-xs bg-indigo-600 text-white rounded hover:bg-indigo-700 transition">
                                            Suivre
                                        </button>
                                    </form>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    <?php else: ?>
                        <p class="text-sm text-gray-500">Aucune suggestion</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Posts Feed - Center/Right Side -->
        <div class="flex-1 max-w-3xl space-y-8">
    <div class="space-y-6">
        <?php $__currentLoopData = $posts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $post): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div
                class="p-6 rounded-2xl shadow-sm border
                    <?php echo e($post->user_id === auth()->id() ? 'bg-white border-gray-200 dark:bg-gray-800 dark:border-gray-700' : 'bg-gray-50 border-gray-300 dark:bg-gray-900 dark:border-gray-700'); ?>">

                <!-- Header user -->
                <div class="flex items-center space-x-4 mb-4">
                    <a href="<?php echo e(route('profile.show', $post->user)); ?>" class="flex-shrink-0">
                        <?php if($post->user->profile_image): ?>
                            <img class="h-10 w-10 rounded-full object-cover"
                                src="<?php echo e(Storage::url($post->user->profile_image)); ?>"
                                alt="<?php echo e($post->user->name); ?>">
                        <?php else: ?>
                            <img class="h-10 w-10 rounded-full"
                                src="https://ui-avatars.com/api/?name=<?php echo e(urlencode($post->user->name)); ?>"
                                alt="<?php echo e($post->user->name); ?>">
                        <?php endif; ?>
                    </a>
                    <div>
                        <a href="<?php echo e(route('profile.show', $post->user)); ?>"
                            class="font-semibold hover:underline text-gray-900 dark:text-gray-100"><?php echo e($post->user->name); ?></a>
                        <p class="text-gray-500 text-sm dark:text-gray-400"><?php echo e($post->created_at->diffForHumans()); ?></p>
                    </div>
                </div>

                <!-- Contenu texte -->
                <?php if($post->content): ?>
                    <p class="mb-4 text-gray-900 dark:text-gray-100"><?php echo e($post->content); ?></p>
                <?php endif; ?>

                <!-- Image -->
                <?php if($post->image_path): ?>
                    <div class="flex justify-center mb-4">
                        <div class="w-[400px] h-[300px]">
                            <img src="<?php echo e(Storage::url($post->image_path)); ?>" alt="Post image"
                                class="rounded-lg w-full h-full object-cover">
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Vidéo -->
                <?php if($post->video_path): ?>
                    <div class="flex justify-center mb-4">
                        <div class="w-[400px] h-[300px]">
                            <video controls class="rounded-lg w-full h-full object-cover">
                                <source src="<?php echo e(Storage::url($post->video_path)); ?>" type="video/mp4">
                                Votre navigateur ne supporte pas la lecture de vidéos.
                            </video>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Barre actions like, commentaire, suppression -->
                <div class="flex items-center justify-between border-t border-gray-200 pt-4 dark:border-gray-700">
                    <div class="flex space-x-4">
                        <!-- Like Button with AJAX -->
                        <button onclick="toggleLike(<?php echo e($post->id); ?>)"
                                id="like-btn-<?php echo e($post->id); ?>"
                                class="flex items-center space-x-2 transition-colors duration-200 <?php echo e($post->isLikedBy(auth()->user()) ? 'text-indigo-600' : 'text-gray-600 hover:text-indigo-600 dark:text-gray-400 dark:hover:text-indigo-400'); ?>">
                            <svg class="w-5 h-5" id="like-icon-<?php echo e($post->id); ?>"
                                 fill="<?php echo e($post->isLikedBy(auth()->user()) ? 'currentColor' : 'none'); ?>"
                                 stroke="currentColor" viewBox="0 0 24 24">
                                <?php if($post->isLikedBy(auth()->user())): ?>
                                    <path d="M2 10.5a1.5 1.5 0 113 0v6a1.5 1.5 0 01-3 0v-6zM6 10.333v5.43a2 2 0 001.106 1.79l.05.025A4 4 0 008.943 18h5.416a2 2 0 001.962-1.608l1.2-6A2 2 0 0015.56 8H12V4a2 2 0 00-2-2 1 1 0 00-1 1v.667a4 4 0 01-.8 2.4L6.8 7.933a4 4 0 00-.8 2.4z"></path>
                                <?php else: ?>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5"></path>
                                <?php endif; ?>
                            </svg>
                            <span id="like-count-<?php echo e($post->id); ?>"
                                  onclick="showLikesModal(<?php echo e($post->id); ?>)"
                                  class="cursor-pointer hover:underline"><?php echo e($post->likes->count()); ?></span>
                        </button>

                        <button type="button"
                            class="flex items-center space-x-2 text-gray-600 hover:text-indigo-600 dark:text-gray-400 dark:hover:text-indigo-400"
                            onclick="document.getElementById('comments-<?php echo e($post->id); ?>').classList.toggle('hidden')">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z">
                                </path>
                            </svg>
                            <span id="comment-count-<?php echo e($post->id); ?>"><?php echo e($post->comments->count()); ?></span>
                        </button>
                    </div>

                    <?php if($post->user_id === auth()->id()): ?>
                        <form action="<?php echo e(route('posts.destroy', $post)); ?>" method="POST" class="flex items-center">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('DELETE'); ?>
                            <button type="submit" class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-600">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                </svg>
                            </button>
                        </form>
                    <?php endif; ?>
                </div>

                <!-- Comments Section -->
                <div id="comments-<?php echo e($post->id); ?>" class="hidden mt-4 space-y-4">
                    <form onsubmit="addComment(event, <?php echo e($post->id); ?>)" class="flex space-x-2">
                        <input type="text" name="content" id="comment-input-<?php echo e($post->id); ?>"
                            class="flex-1 border-gray-300 rounded-md shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-100"
                            placeholder="Ajouter un commentaire..." required>
                        <button type="submit" id="comment-btn-<?php echo e($post->id); ?>"
                            class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50">
                            Commenter
                        </button>
                    </form>

                    <div id="comments-list-<?php echo e($post->id); ?>">
                    <?php $__currentLoopData = $post->comments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $comment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="flex space-x-4">
                            <div class="flex-shrink-0">
                                <?php if($comment->user->profile_image): ?>
                                    <img class="h-8 w-8 rounded-full object-cover"
                                        src="<?php echo e(Storage::url($comment->user->profile_image)); ?>"
                                        alt="<?php echo e($comment->user->name); ?>">
                                <?php else: ?>
                                    <img class="h-8 w-8 rounded-full"
                                        src="https://ui-avatars.com/api/?name=<?php echo e(urlencode($comment->user->name)); ?>"
                                        alt="<?php echo e($comment->user->name); ?>">
                                <?php endif; ?>
                            </div>
                            <div class="flex-1">
                                <div class="bg-gray-50 rounded-lg p-3 dark:bg-gray-800">
                                    <div class="flex items-center justify-between">
                                        <a href="<?php echo e(route('profile.show', $comment->user)); ?>"
                                            class="font-semibold hover:underline text-gray-900 dark:text-gray-100"><?php echo e($comment->user->name); ?></a>
                                        <span class="text-gray-500 text-sm dark:text-gray-400"><?php echo e($comment->created_at->diffForHumans()); ?></span>
                                    </div>
                                    <p class="mt-1 text-gray-900 dark:text-gray-100"><?php echo e($comment->content); ?></p>
                                </div>
                                <?php if($comment->user_id === auth()->id()): ?>
                                    <form action="<?php echo e(route('posts.comments.destroy', [$post, $comment])); ?>" method="POST"
                                        class="mt-1">
                                        <?php echo csrf_field(); ?>
                                        <?php echo method_field('DELETE'); ?>
                                        <button
                                            class="text-sm text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-600">Supprimer</button>
                                    </form>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </div> <!-- fin du post container -->
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

        <!-- Pagination -->
        <div class="mt-6">
            <?php echo e($posts->links()); ?>

        </div>
    </div>
        </div>


    </div>
</div>

<!-- Modal pour afficher les likes -->
<div id="likes-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
    <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl max-w-md w-full max-h-[80vh] overflow-hidden">
        <!-- Header du modal -->
        <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                Personnes qui ont aimé
            </h3>
            <button onclick="closeLikesModal()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>

        <!-- Contenu du modal -->
        <div class="p-6">
            <!-- Loading state -->
            <div id="likes-loading" class="flex items-center justify-center py-8">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
                <span class="ml-3 text-gray-600 dark:text-gray-400">Chargement...</span>
            </div>

            <!-- Liste des likes -->
            <div id="likes-list" class="hidden space-y-3 max-h-96 overflow-y-auto">
                <!-- Les likes seront ajoutés ici dynamiquement -->
            </div>

            <!-- État vide -->
            <div id="likes-empty" class="hidden text-center py-8">
                <svg class="w-16 h-16 mx-auto text-gray-400 dark:text-gray-500 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                </svg>
                <p class="text-gray-500 dark:text-gray-400">Aucun like pour le moment</p>
            </div>
        </div>
    </div>
</div>

    <script>
function previewImage(input) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const imagePreview = document.getElementById('image-preview');
            const videoPreview = document.getElementById('video-preview');
            const mediaPreview = document.getElementById('media-preview');

            imagePreview.querySelector('img').src = e.target.result;
            imagePreview.classList.remove('hidden');
            videoPreview.classList.add('hidden');
            mediaPreview.classList.remove('hidden');

            // Reset video input to prevent submitting old file
            const videoInput = document.querySelector('input[name="video"]');
            if (videoInput) {
                videoInput.value = "";
            }
        };
        reader.readAsDataURL(input.files[0]);
    }
}

function previewVideo(input) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const videoPreview = document.getElementById('video-preview');
            const imagePreview = document.getElementById('image-preview');
            const mediaPreview = document.getElementById('media-preview');

            videoPreview.querySelector('source').src = e.target.result;
            videoPreview.querySelector('video').load();
            videoPreview.classList.remove('hidden');
            imagePreview.classList.add('hidden');
            mediaPreview.classList.remove('hidden');

            // Reset image input to prevent submitting old file
            const imageInput = document.querySelector('input[name="image"]');
            if (imageInput) {
                imageInput.value = "";
            }
        };
        reader.readAsDataURL(input.files[0]);
    }
}

function removeMedia(type) {
    if (type === 'image') {
        const imageInput = document.querySelector('input[name="image"]');
        if (imageInput) {
            imageInput.value = "";
        }
        document.getElementById('image-preview').classList.add('hidden');
    } else if (type === 'video') {
        const videoInput = document.querySelector('input[name="video"]');
        if (videoInput) {
            videoInput.value = "";
        }
        document.getElementById('video-preview').classList.add('hidden');
    }

    // If both previews hidden, hide the container
    const imageHidden = document.getElementById('image-preview').classList.contains('hidden');
    const videoHidden = document.getElementById('video-preview').classList.contains('hidden');
    if (imageHidden && videoHidden) {
        document.getElementById('media-preview').classList.add('hidden');
    }
}

function toggleMobileNotifications() {
    const panel = document.getElementById('mobile-notifications');
    const arrow = document.getElementById('mobile-notifications-arrow');

    if (panel.classList.contains('hidden')) {
        panel.classList.remove('hidden');
        arrow.style.transform = 'rotate(180deg)';
    } else {
        panel.classList.add('hidden');
        arrow.style.transform = 'rotate(0deg)';
    }
}

// Fonction pour gérer les likes avec AJAX
async function toggleLike(postId) {
    const likeBtn = document.getElementById(`like-btn-${postId}`);
    const likeIcon = document.getElementById(`like-icon-${postId}`);
    const likeCount = document.getElementById(`like-count-${postId}`);

    // Désactiver le bouton temporairement
    likeBtn.disabled = true;
    likeBtn.style.opacity = '0.6';

    try {
        // Déterminer si c'est un like ou unlike basé sur la couleur actuelle
        const isCurrentlyLiked = likeBtn.classList.contains('text-indigo-600');
        const url = isCurrentlyLiked ? `/posts/${postId}/like` : `/posts/${postId}/like`;
        const method = isCurrentlyLiked ? 'DELETE' : 'POST';

        const response = await axios({
            method: method,
            url: url,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            }
        });

        if (response.data.success) {
            // Mettre à jour le compteur
            likeCount.textContent = response.data.likes_count;

            // Mettre à jour l'apparence du bouton
            if (response.data.liked) {
                // Post aimé
                likeBtn.className = 'flex items-center space-x-2 transition-colors duration-200 text-indigo-600';
                likeIcon.setAttribute('fill', 'currentColor');
                likeIcon.innerHTML = '<path d="M2 10.5a1.5 1.5 0 113 0v6a1.5 1.5 0 01-3 0v-6zM6 10.333v5.43a2 2 0 001.106 1.79l.05.025A4 4 0 008.943 18h5.416a2 2 0 001.962-1.608l1.2-6A2 2 0 0015.56 8H12V4a2 2 0 00-2-2 1 1 0 00-1 1v.667a4 4 0 01-.8 2.4L6.8 7.933a4 4 0 00-.8 2.4z"></path>';
            } else {
                // Post pas aimé
                likeBtn.className = 'flex items-center space-x-2 transition-colors duration-200 text-gray-600 hover:text-indigo-600 dark:text-gray-400 dark:hover:text-indigo-400';
                likeIcon.setAttribute('fill', 'none');
                likeIcon.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5"></path>';
            }

            // Animation de feedback
            likeBtn.style.transform = 'scale(1.1)';
            setTimeout(() => {
                likeBtn.style.transform = 'scale(1)';
            }, 150);
        }
    } catch (error) {
        console.error('Erreur lors du like:', error);
        // Afficher un message d'erreur
        showNotification('Erreur lors du like', 'error');
    } finally {
        // Réactiver le bouton
        likeBtn.disabled = false;
        likeBtn.style.opacity = '1';
    }
}

// Fonction pour ajouter un commentaire avec AJAX
async function addComment(event, postId) {
    event.preventDefault();

    const commentInput = document.getElementById(`comment-input-${postId}`);
    const commentBtn = document.getElementById(`comment-btn-${postId}`);
    const commentsList = document.getElementById(`comments-list-${postId}`);
    const commentCount = document.getElementById(`comment-count-${postId}`);

    const content = commentInput.value.trim();
    if (!content) return;

    // Désactiver le formulaire
    commentBtn.disabled = true;
    commentBtn.textContent = 'Envoi...';

    try {
        const response = await axios.post(`/posts/${postId}/comments`, {
            content: content
        }, {
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            }
        });

        if (response.data.success) {
            // Mettre à jour le compteur
            commentCount.textContent = response.data.comments_count;

            // Ajouter le nouveau commentaire à la liste
            const newComment = createCommentElement(response.data.comment);
            commentsList.appendChild(newComment);

            // Vider le champ de saisie
            commentInput.value = '';

            // Animation d'apparition
            newComment.style.opacity = '0';
            newComment.style.transform = 'translateY(20px)';
            setTimeout(() => {
                newComment.style.transition = 'all 0.3s ease-out';
                newComment.style.opacity = '1';
                newComment.style.transform = 'translateY(0)';
            }, 10);

            showNotification('Commentaire ajouté !', 'success');
        }
    } catch (error) {
        console.error('Erreur lors de l\'ajout du commentaire:', error);
        showNotification('Erreur lors de l\'ajout du commentaire', 'error');
    } finally {
        // Réactiver le formulaire
        commentBtn.disabled = false;
        commentBtn.textContent = 'Commenter';
    }
}

// Fonction pour créer un élément commentaire
function createCommentElement(comment) {
    const div = document.createElement('div');
    div.className = 'flex space-x-4';

    const profileImage = comment.user.profile_image
        ? `/storage/${comment.user.profile_image}`
        : `https://ui-avatars.com/api/?name=${encodeURIComponent(comment.user.name)}`;

    div.innerHTML = `
        <div class="flex-shrink-0">
            <img class="h-8 w-8 rounded-full object-cover"
                 src="${profileImage}"
                 alt="${comment.user.name}">
        </div>
        <div class="flex-1">
            <div class="bg-gray-50 rounded-lg p-3 dark:bg-gray-800">
                <div class="flex items-center justify-between">
                    <a href="/profile/${comment.user.id}"
                       class="font-semibold hover:underline text-gray-900 dark:text-gray-100">${comment.user.name}</a>
                    <span class="text-gray-500 text-sm dark:text-gray-400">À l'instant</span>
                </div>
                <p class="mt-1 text-gray-900 dark:text-gray-100">${comment.content}</p>
            </div>
        </div>
    `;

    return div;
}

// Fonction pour afficher des notifications toast
function showNotification(message, type = 'success') {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 px-6 py-3 rounded-lg text-white z-50 transition-all duration-300 ${
        type === 'success' ? 'bg-green-500' : 'bg-red-500'
    }`;
    notification.textContent = message;

    document.body.appendChild(notification);

    // Animation d'apparition
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
        notification.style.opacity = '1';
    }, 10);

    // Suppression automatique
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        notification.style.opacity = '0';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Fonctions pour gérer le modal des likes
async function showLikesModal(postId) {
    const modal = document.getElementById('likes-modal');
    const loading = document.getElementById('likes-loading');
    const likesList = document.getElementById('likes-list');
    const likesEmpty = document.getElementById('likes-empty');

    // Afficher le modal
    modal.classList.remove('hidden');
    document.body.style.overflow = 'hidden'; // Empêcher le scroll de la page

    // Réinitialiser l'état
    loading.classList.remove('hidden');
    likesList.classList.add('hidden');
    likesEmpty.classList.add('hidden');
    likesList.innerHTML = '';

    try {
        const response = await axios.get(`/posts/${postId}/likes`);

        if (response.data.success) {
            const likes = response.data.likes;

            loading.classList.add('hidden');

            if (likes.length === 0) {
                likesEmpty.classList.remove('hidden');
            } else {
                likesList.classList.remove('hidden');

                // Créer les éléments pour chaque like
                likes.forEach(like => {
                    const likeElement = createLikeElement(like);
                    likesList.appendChild(likeElement);
                });
            }
        }
    } catch (error) {
        console.error('Erreur lors du chargement des likes:', error);
        loading.classList.add('hidden');

        // Afficher un message d'erreur
        likesList.innerHTML = `
            <div class="text-center py-8">
                <p class="text-red-500">Erreur lors du chargement des likes</p>
                <button onclick="showLikesModal(${postId})" class="mt-2 text-indigo-600 hover:text-indigo-800">
                    Réessayer
                </button>
            </div>
        `;
        likesList.classList.remove('hidden');
    }
}

function closeLikesModal() {
    const modal = document.getElementById('likes-modal');
    modal.classList.add('hidden');
    document.body.style.overflow = 'auto'; // Rétablir le scroll de la page
}

// Créer un élément pour afficher un like
function createLikeElement(like) {
    const div = document.createElement('div');
    div.className = 'flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors';

    const profileImage = like.profile_image_url
        ? like.profile_image_url
        : `https://ui-avatars.com/api/?name=${encodeURIComponent(like.name)}&size=40`;

    div.innerHTML = `
        <div class="flex items-center space-x-3">
            <a href="/profile/${like.id}" class="flex-shrink-0">
                <img class="h-10 w-10 rounded-full object-cover border-2 border-gray-200 dark:border-gray-600"
                     src="${profileImage}"
                     alt="${like.name}">
            </a>
            <div>
                <a href="/profile/${like.id}" class="font-medium text-gray-900 dark:text-gray-100 hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors">
                    ${like.name}
                </a>
                <p class="text-sm text-gray-500 dark:text-gray-400">${like.liked_at}</p>
            </div>
        </div>
        <div class="flex items-center space-x-2">
            ${like.is_following ?
                `<button onclick="unfollowUser(${like.id})" class="px-3 py-1 text-sm bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-full hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors">
                    Suivi(e)
                </button>` :
                `<button onclick="followUser(${like.id})" class="px-3 py-1 text-sm bg-indigo-600 text-white rounded-full hover:bg-indigo-700 transition-colors">
                    Suivre
                </button>`
            }
            <svg class="w-5 h-5 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                <path d="M2 10.5a1.5 1.5 0 113 0v6a1.5 1.5 0 01-3 0v-6zM6 10.333v5.43a2 2 0 001.106 1.79l.05.025A4 4 0 008.943 18h5.416a2 2 0 001.962-1.608l1.2-6A2 2 0 0015.56 8H12V4a2 2 0 00-2-2 1 1 0 00-1 1v.667a4 4 0 01-.8 2.4L6.8 7.933a4 4 0 00-.8 2.4z"></path>
            </svg>
        </div>
    `;

    return div;
}

// Fonctions pour suivre/ne plus suivre (optionnelles)
async function followUser(userId) {
    try {
        const response = await axios.post(`/users/${userId}/follow`, {}, {
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            }
        });

        if (response.data.success) {
            showNotification('Utilisateur suivi !', 'success');
            // Mettre à jour le bouton
            const button = event.target;
            button.textContent = 'Suivi(e)';
            button.className = 'px-3 py-1 text-sm bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-full hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors';
            button.onclick = () => unfollowUser(userId);
        }
    } catch (error) {
        console.error('Erreur lors du suivi:', error);
        showNotification('Erreur lors du suivi', 'error');
    }
}

async function unfollowUser(userId) {
    try {
        const response = await axios.delete(`/users/${userId}/follow`, {
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            }
        });

        if (response.data.success) {
            showNotification('Utilisateur non suivi', 'success');
            // Mettre à jour le bouton
            const button = event.target;
            button.textContent = 'Suivre';
            button.className = 'px-3 py-1 text-sm bg-indigo-600 text-white rounded-full hover:bg-indigo-700 transition-colors';
            button.onclick = () => followUser(userId);
        }
    } catch (error) {
        console.error('Erreur lors du désabonnement:', error);
        showNotification('Erreur lors du désabonnement', 'error');
    }
}

// Fermer le modal en cliquant à l'extérieur
document.getElementById('likes-modal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeLikesModal();
    }
});

// Fermer le modal avec la touche Escape
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        const modal = document.getElementById('likes-modal');
        if (!modal.classList.contains('hidden')) {
            closeLikesModal();
        }
    }
});

    </script>

    <style>
    .notifications-sidebar {
        overflow-y: auto;
        scrollbar-width: thin;
        scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
    }

    .notifications-sidebar::-webkit-scrollbar {
        width: 6px;
    }

    .notifications-sidebar::-webkit-scrollbar-track {
        background: transparent;
    }

    .notifications-sidebar::-webkit-scrollbar-thumb {
        background-color: rgba(156, 163, 175, 0.5);
        border-radius: 3px;
    }

    .notifications-sidebar::-webkit-scrollbar-thumb:hover {
        background-color: rgba(156, 163, 175, 0.7);
    }

    /* Dark mode scrollbar */
    .dark .notifications-sidebar::-webkit-scrollbar-thumb {
        background-color: rgba(75, 85, 99, 0.5);
    }

    .dark .notifications-sidebar::-webkit-scrollbar-thumb:hover {
        background-color: rgba(75, 85, 99, 0.7);
    }
    </style>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?><?php /**PATH C:\Users\<USER>\Desktop\euromedconnect\application\resources\views/home.blade.php ENDPATH**/ ?>