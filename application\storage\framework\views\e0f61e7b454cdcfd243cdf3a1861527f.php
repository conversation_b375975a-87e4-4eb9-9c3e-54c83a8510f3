<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <!-- Create Post Form - Large Container -->
    <div class="w-full max-w-screen-xl mx-auto px-4 sm:px-6 lg:px-8 mb-10">
        <form action="<?php echo e(route('posts.store')); ?>" method="POST" enctype="multipart/form-data" class="space-y-6">
            <?php echo csrf_field(); ?>

            <!-- Zone de saisie -->
            <div class="bg-white dark:bg-indigo-800 p-4 rounded-xl shadow-md border border-gray-200 dark:border-indigo-700">
                <textarea name="content" rows="4"
                    class="w-full bg-white dark:bg-indigo-900 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-300 border border-gray-300 dark:border-indigo-700 rounded-md shadow-sm
                           focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:focus:ring-indigo-400 focus:border-indigo-500 dark:focus:border-indigo-400 resize-none"
                    placeholder="Quoi de neuf ?"></textarea>

                 <!-- Prévisualisation des médias -->
             <div id="media-preview" class="mt-4">
                <div id="image-preview" class="hidden  p-4 rounded-sm shadow-md ">
                    <img src="" alt="Prévisualisation" class="max-h-48 rounded-lg">
                    <button type="button" onclick="removeMedia('image')" class="mt-2 text-red-600 hover:text-red-900">
                        Supprimer l'image
                    </button>
                </div>
                <div id="video-preview" class="hidden bg-white p-4 rounded-sm shadow-md border border-gray-200">
                    <video controls class="max-h-48 rounded-lg">
                        <source src="" type="video/mp4">
                        Votre navigateur ne supporte pas la lecture de vidéos.
                    </video>
                    <button aria-label="Supprimer la vidéo" type="button" onclick="removeMedia('video')" class="mt-2 text-red-600 hover:text-red-900">
                        Supprimer la vidéo
                    </button>
                </div>
            </div>
            </div>




            <!-- Zone de sélection fichiers -->
            <div class=" p-4 rounded-sm  ">
                <div class="flex items-center justify-between">
                    <div class="flex space-x-4">
                        <label class="cursor-pointer">
                            <input type="file" name="image" class="hidden" accept="image/*" onchange="previewImage(this)">
                            <span class="flex items-center space-x-2 text-gray-600 hover:text-gray-900">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z">
                                    </path>
                                </svg>
                                <span>Photo</span>
                            </span>
                        </label>
                        <label class="cursor-pointer">
                            <input type="file" name="video" class="hidden" accept="video/*" onchange="previewVideo(this)">
                            <span class="flex items-center space-x-2 text-gray-600 hover:text-gray-900">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z">
                                    </path>
                                </svg>
                                <span>Vidéo</span>
                            </span>
                        </label>
                    </div>

                    <button type="submit"
                        class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        Publier
                    </button>
                </div>
            </div>


        </form>
    </div>


<!-- Main Content with Sidebar Layout -->
<div class="max-w-screen-xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex gap-8">
        <!-- Posts Feed - Left Side -->
        <div class="flex-1 max-w-3xl space-y-8">
    <div class="space-y-6">
        <?php $__currentLoopData = $posts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $post): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div
                class="p-6 rounded-2xl shadow-sm border
                    <?php echo e($post->user_id === auth()->id() ? 'bg-white border-gray-200 dark:bg-gray-800 dark:border-gray-700' : 'bg-gray-50 border-gray-300 dark:bg-gray-900 dark:border-gray-700'); ?>">

                <!-- Header user -->
                <div class="flex items-center space-x-4 mb-4">
                    <a href="<?php echo e(route('profile.show', $post->user)); ?>" class="flex-shrink-0">
                        <?php if($post->user->profile_image): ?>
                            <img class="h-10 w-10 rounded-full object-cover"
                                src="<?php echo e(Storage::url($post->user->profile_image)); ?>"
                                alt="<?php echo e($post->user->name); ?>">
                        <?php else: ?>
                            <img class="h-10 w-10 rounded-full"
                                src="https://ui-avatars.com/api/?name=<?php echo e(urlencode($post->user->name)); ?>"
                                alt="<?php echo e($post->user->name); ?>">
                        <?php endif; ?>
                    </a>
                    <div>
                        <a href="<?php echo e(route('profile.show', $post->user)); ?>"
                            class="font-semibold hover:underline text-gray-900 dark:text-gray-100"><?php echo e($post->user->name); ?></a>
                        <p class="text-gray-500 text-sm dark:text-gray-400"><?php echo e($post->created_at->diffForHumans()); ?></p>
                    </div>
                </div>

                <!-- Contenu texte -->
                <?php if($post->content): ?>
                    <p class="mb-4 text-gray-900 dark:text-gray-100"><?php echo e($post->content); ?></p>
                <?php endif; ?>

                <!-- Image -->
                <?php if($post->image_path): ?>
                    <div class="flex justify-center mb-4">
                        <div class="w-[400px] h-[300px]">
                            <img src="<?php echo e(Storage::url($post->image_path)); ?>" alt="Post image"
                                class="rounded-lg w-full h-full object-cover">
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Vidéo -->
                <?php if($post->video_path): ?>
                    <div class="flex justify-center mb-4">
                        <div class="w-[400px] h-[300px]">
                            <video controls class="rounded-lg w-full h-full object-cover">
                                <source src="<?php echo e(Storage::url($post->video_path)); ?>" type="video/mp4">
                                Votre navigateur ne supporte pas la lecture de vidéos.
                            </video>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Barre actions like, commentaire, suppression -->
                <div class="flex items-center justify-between border-t border-gray-200 pt-4 dark:border-gray-700">
                    <div class="flex space-x-4">
                        <form action="<?php echo e(route('posts.like', $post)); ?>" method="POST">
                            <?php echo csrf_field(); ?>
                            <?php if($post->isLikedBy(auth()->user())): ?>
                                <?php echo method_field('DELETE'); ?>
                                <button type="submit" class="flex items-center space-x-2 text-indigo-600">
                                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                        <path
                                            d="M2 10.5a1.5 1.5 0 113 0v6a1.5 1.5 0 01-3 0v-6zM6 10.333v5.43a2 2 0 001.106 1.79l.05.025A4 4 0 008.943 18h5.416a2 2 0 001.962-1.608l1.2-6A2 2 0 0015.56 8H12V4a2 2 0 00-2-2 1 1 0 00-1 1v.667a4 4 0 01-.8 2.4L6.8 7.933a4 4 0 00-.8 2.4z">
                                        </path>
                                    </svg>
                                    <span><?php echo e($post->likes->count()); ?></span>
                                </button>
                            <?php else: ?>
                                <button type="submit"
                                    class="flex items-center space-x-2 text-gray-600 hover:text-indigo-600 dark:text-gray-400 dark:hover:text-indigo-400">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5">
                                        </path>
                                    </svg>
                                    <span><?php echo e($post->likes->count()); ?></span>
                                </button>
                            <?php endif; ?>
                        </form>

                        <button type="button"
                            class="flex items-center space-x-2 text-gray-600 hover:text-indigo-600 dark:text-gray-400 dark:hover:text-indigo-400"
                            onclick="document.getElementById('comments-<?php echo e($post->id); ?>').classList.toggle('hidden')">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z">
                                </path>
                            </svg>
                            <span><?php echo e($post->comments->count()); ?></span>
                        </button>
                    </div>

                    <?php if($post->user_id === auth()->id()): ?>
                        <form action="<?php echo e(route('posts.destroy', $post)); ?>" method="POST" class="flex items-center">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('DELETE'); ?>
                            <button type="submit" class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-600">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                </svg>
                            </button>
                        </form>
                    <?php endif; ?>
                </div>

                <!-- Comments Section -->
                <div id="comments-<?php echo e($post->id); ?>" class="hidden mt-4 space-y-4">
                    <form action="<?php echo e(route('posts.comments.store', $post)); ?>" method="POST" class="flex space-x-2">
                        <?php echo csrf_field(); ?>
                        <input type="text" name="content"
                            class="flex-1 border-gray-300 rounded-md shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-100"
                            placeholder="Ajouter un commentaire...">
                        <button type="submit"
                            class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            Commenter
                        </button>
                    </form>

                    <?php $__currentLoopData = $post->comments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $comment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="flex space-x-4">
                            <div class="flex-shrink-0">
                                <?php if($comment->user->profile_image): ?>
                                    <img class="h-8 w-8 rounded-full object-cover"
                                        src="<?php echo e(Storage::url($comment->user->profile_image)); ?>"
                                        alt="<?php echo e($comment->user->name); ?>">
                                <?php else: ?>
                                    <img class="h-8 w-8 rounded-full"
                                        src="https://ui-avatars.com/api/?name=<?php echo e(urlencode($comment->user->name)); ?>"
                                        alt="<?php echo e($comment->user->name); ?>">
                                <?php endif; ?>
                            </div>
                            <div class="flex-1">
                                <div class="bg-gray-50 rounded-lg p-3 dark:bg-gray-800">
                                    <div class="flex items-center justify-between">
                                        <a href="<?php echo e(route('profile.show', $comment->user)); ?>"
                                            class="font-semibold hover:underline text-gray-900 dark:text-gray-100"><?php echo e($comment->user->name); ?></a>
                                        <span class="text-gray-500 text-sm dark:text-gray-400"><?php echo e($comment->created_at->diffForHumans()); ?></span>
                                    </div>
                                    <p class="mt-1 text-gray-900 dark:text-gray-100"><?php echo e($comment->content); ?></p>
                                </div>
                                <?php if($comment->user_id === auth()->id()): ?>
                                    <form action="<?php echo e(route('posts.comments.destroy', [$post, $comment])); ?>" method="POST"
                                        class="mt-1">
                                        <?php echo csrf_field(); ?>
                                        <?php echo method_field('DELETE'); ?>
                                        <button
                                            class="text-sm text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-600">Supprimer</button>
                                    </form>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div> <!-- fin du post container -->
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

        <!-- Pagination -->
        <div class="mt-6">
            <?php echo e($posts->links()); ?>

        </div>
    </div>
        </div>

        <!-- Notifications Sidebar - Right Side -->
        <div class="hidden lg:block">
            <?php if (isset($component)) { $__componentOriginal91cea93a658deb43affdd4d31a514a4e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal91cea93a658deb43affdd4d31a514a4e = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.notifications-sidebar','data' => ['notifications' => $notifications,'friendSuggestions' => $friendSuggestions,'friendsActivity' => $friendsActivity]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('notifications-sidebar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['notifications' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($notifications),'friendSuggestions' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($friendSuggestions),'friendsActivity' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($friendsActivity)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal91cea93a658deb43affdd4d31a514a4e)): ?>
<?php $attributes = $__attributesOriginal91cea93a658deb43affdd4d31a514a4e; ?>
<?php unset($__attributesOriginal91cea93a658deb43affdd4d31a514a4e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal91cea93a658deb43affdd4d31a514a4e)): ?>
<?php $component = $__componentOriginal91cea93a658deb43affdd4d31a514a4e; ?>
<?php unset($__componentOriginal91cea93a658deb43affdd4d31a514a4e); ?>
<?php endif; ?>
        </div>
    </div>
</div>



    <script>
function previewImage(input) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const imagePreview = document.getElementById('image-preview');
            const videoPreview = document.getElementById('video-preview');
            const mediaPreview = document.getElementById('media-preview');

            imagePreview.querySelector('img').src = e.target.result;
            imagePreview.classList.remove('hidden');
            videoPreview.classList.add('hidden');
            mediaPreview.classList.remove('hidden');

            // Reset video input to prevent submitting old file
            const videoInput = document.querySelector('input[name="video"]');
            if (videoInput) {
                videoInput.value = "";
            }
        };
        reader.readAsDataURL(input.files[0]);
    }
}

function previewVideo(input) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const videoPreview = document.getElementById('video-preview');
            const imagePreview = document.getElementById('image-preview');
            const mediaPreview = document.getElementById('media-preview');

            videoPreview.querySelector('source').src = e.target.result;
            videoPreview.querySelector('video').load();
            videoPreview.classList.remove('hidden');
            imagePreview.classList.add('hidden');
            mediaPreview.classList.remove('hidden');

            // Reset image input to prevent submitting old file
            const imageInput = document.querySelector('input[name="image"]');
            if (imageInput) {
                imageInput.value = "";
            }
        };
        reader.readAsDataURL(input.files[0]);
    }
}

function removeMedia(type) {
    if (type === 'image') {
        const imageInput = document.querySelector('input[name="image"]');
        if (imageInput) {
            imageInput.value = "";
        }
        document.getElementById('image-preview').classList.add('hidden');
    } else if (type === 'video') {
        const videoInput = document.querySelector('input[name="video"]');
        if (videoInput) {
            videoInput.value = "";
        }
        document.getElementById('video-preview').classList.add('hidden');
    }

    // If both previews hidden, hide the container
    const imageHidden = document.getElementById('image-preview').classList.contains('hidden');
    const videoHidden = document.getElementById('video-preview').classList.contains('hidden');
    if (imageHidden && videoHidden) {
        document.getElementById('media-preview').classList.add('hidden');
    }
}

    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?><?php /**PATH C:\Users\<USER>\Desktop\euromedconnect\application\resources\views/home.blade.php ENDPATH**/ ?>