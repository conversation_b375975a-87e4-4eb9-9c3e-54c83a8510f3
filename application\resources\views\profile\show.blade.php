<x-app-layout>
    <div class="max-w-7xl mx-auto py-10 sm:px-6 lg:px-8">
        <div class="bg-white dark:bg-gray-900 overflow-hidden shadow-sm sm:rounded-lg transition-colors duration-300">
            <div class="p-6">
                <div class="flex items-center justify-between mb-8">
                    <div class="flex items-center space-x-4">
                        <img class="h-20 w-20 rounded-full" src="https://ui-avatars.com/api/?name={{ urlencode($user->name) }}" alt="{{ $user->name }}">
                        <div>
                            <h2 class="text-2xl font-bold text-gray-900 dark:text-white">{{ $user->name }}</h2>
                            <p class="text-gray-500 dark:text-gray-400">{{ '@' . Str::slug($user->name) }}</p>
                        </div>
                    </div>
                    @if(Auth::id() !== $user->id)
                        <form action="{{ route('users.follow', $user) }}" method="POST">
                            @csrf
                            @if($isFollowing)
                                @method('DELETE')
                                <button type="submit" class="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors">
                                    Ne plus suivre
                                </button>
                            @else
                                <button type="submit" class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors">
                                    Suivre
                                </button>
                            @endif
                        </form>
                    @endif
                </div>

                <div class="flex justify-center space-x-8 mb-8 text-gray-700 dark:text-gray-200">
                    <div class="text-center">
                        <span class="block text-2xl font-bold">{{ $posts->total() }}</span>
                        <span class="text-gray-500 dark:text-gray-400">Posts</span>
                    </div>
                    <div class="text-center">
                        <span class="block text-2xl font-bold">{{ $followersCount }}</span>
                        <span class="text-gray-500 dark:text-gray-400">Abonnés</span>
                    </div>
                    <div class="text-center">
                        <span class="block text-2xl font-bold">{{ $followingCount }}</span>
                        <span class="text-gray-500 dark:text-gray-400">Abonnements</span>
                    </div>
                </div>

                <div class="space-y-6">
                    @foreach($posts as $post)
                        <div class="border-t border-gray-200 dark:border-gray-700 pt-6 transition-colors duration-300">
                            <div class="flex items-center space-x-4 mb-4">
                                <img class="h-10 w-10 rounded-full" src="https://ui-avatars.com/api/?name={{ urlencode($post->user->name) }}" alt="{{ $post->user->name }}">
                                <div>
                                    <a href="{{ route('profile.show', $post->user) }}" class="font-semibold text-gray-900 dark:text-white hover:underline">{{ $post->user->name }}</a>
                                    <p class="text-gray-500 dark:text-gray-400 text-sm">{{ $post->created_at->diffForHumans() }}</p>
                                </div>
                            </div>

                            @if($post->content)
                                <p class="mb-4 text-gray-800 dark:text-gray-200">{{ $post->content }}</p>
                            @endif

                            @if($post->image_path)
                                <div class="w-full max-w-3xl mx-auto mb-4">
                                    <img src="{{ Storage::url($post->image_path) }}" alt="Post image"
                                        class="rounded-lg w-full max-h-[400px] object-cover shadow-md" />
                                </div>
                            @endif

                            @if($post->video_path)
                                <div class="w-full max-w-3xl mx-auto mb-4">
                                    <video controls
                                        class="rounded-lg w-full max-h-[500px] object-contain shadow-md bg-black">
                                        <source src="{{ Storage::url($post->video_path) }}" type="video/mp4">
                                        Votre navigateur ne supporte pas la lecture de vidéos.
                                    </video>
                                </div>
                            @endif


                            <div class="flex items-center justify-between">
                                <div class="flex space-x-4">
                                    <form action="{{ route('posts.like', $post) }}" method="POST">
                                        @csrf
                                        @if($post->isLikedBy(auth()->user()))
                                            @method('DELETE')
                                            <button type="submit" class="flex items-center space-x-2 text-indigo-600 dark:text-indigo-400">
                                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                                    <path d="M2 10.5a1.5 1.5 0 113 0v6a1.5 1.5 0 01-3 0v-6zM6 10.333v5.43a2 2 0 001.106 1.79l.05.025A4 4 0 008.943 18h5.416a2 2 0 001.962-1.608l1.2-6A2 2 0 0015.56 8H12V4a2 2 0 00-2-2 1 1 0 00-1 1v.667a4 4 0 01-.8 2.4L6.8 7.933a4 4 0 00-.8 2.4z"/>
                                                </svg>
                                                <span>{{ $post->likes->count() }}</span>
                                            </button>
                                        @else
                                            <button type="submit" class="flex items-center space-x-2 text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors">
                                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5"/>
                                                </svg>
                                                <span>{{ $post->likes->count() }}</span>
                                            </button>
                                        @endif
                                    </form>
                                </div>
                            </div>
                        </div>
                    @endforeach

                    <div class="mt-4">
                        {{ $posts->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
