<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">

        <title>{{ config('app.name', 'Uconnect') }}</title>

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

        <!-- Scripts -->
        @vite(['resources/css/app.css', 'resources/js/app.js'])
    </head>
    <body class="font-sans text-gray-900 antialiased">
        <div class="min-h-screen flex flex-col sm:justify-center items-center pt-6 sm:pt-0 bg-gradient-to-br from-indigo-100 to-white">
            <div class="w-full sm:max-w-md px-6 py-4 bg-white shadow-lg overflow-hidden sm:rounded-lg">
                <div class="flex flex-col items-center mb-8">
                    <a href="/" class="flex items-center justify-center">
                        <x-application-logo class="w-auto h-12 fill-current text-indigo-900" />
                    </a>
                    <h2 class="mt-2 text-xl font-bold text-blue-900">
                        {{ config('app.name', 'Uconnect') }}
                    </h2>
                </div>

                {{ $slot }}
            </div>
        </div>
    </body>
</html>
