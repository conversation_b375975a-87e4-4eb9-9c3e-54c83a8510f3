<x-app-layout>
  

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">
            <!-- Profile Section -->
            <div class="relative overflow-hidden rounded-lg shadow-lg">
                <!-- Animated Background -->
                <div class="absolute inset-0 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 opacity-90">
                    <div class="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiNmZmZmZmYiIGZpbGwtb3BhY2l0eT0iMC40Ij48cGF0aCBkPSJNMzYgMzRjMC0yLjIxLTEuNzktNC00LTRzLTQgMS43OS00IDQgMS43OSA0IDQgNCA0LTEuNzkgNC00eiIvPjwvZz48L2c+PC9zdmc+')] opacity-20"></div>
                    <div class="absolute inset-0 bg-gradient-to-b from-transparent to-black opacity-30"></div>
                </div>
                
                <!-- Animated Particles -->
                <div class="absolute inset-0">
                    <div class="absolute w-2 h-2 bg-white rounded-full animate-float" style="top: 20%; left: 10%; animation-delay: 0s;"></div>
                    <div class="absolute w-3 h-3 bg-white rounded-full animate-float" style="top: 60%; left: 20%; animation-delay: 1s;"></div>
                    <div class="absolute w-2 h-2 bg-white rounded-full animate-float" style="top: 30%; left: 80%; animation-delay: 2s;"></div>
                    <div class="absolute w-3 h-3 bg-white rounded-full animate-float" style="top: 70%; left: 70%; animation-delay: 3s;"></div>
                </div>

                <!-- Profile Content -->
                <div class="relative">
                    <!-- Cover Image -->
                    <div class="h-32 bg-gradient-to-r from-indigo-500 to-purple-600 opacity-50"></div>
                    
                    <!-- Profile Info -->
                    <div class="px-6 pb-6">
                        <div class="flex flex-col sm:flex-row items-center sm:items-end -mt-8 sm:-mt-8">
                            <!-- Profile Image -->
                            <div class="relative group">
                                @if(Auth::user()->profile_image)
                                    <img src="{{ Storage::url(Auth::user()->profile_image) }}" 
                                         alt="{{ Auth::user()->name }}" 
                                         class="h-16 w-16 rounded-full border-2 border-white object-cover shadow-lg transition-transform duration-300 group-hover:scale-110">
                                @else
                                    <img src="https://ui-avatars.com/api/?name={{ urlencode(Auth::user()->name) }}&size=64" 
                                         alt="{{ Auth::user()->name }}" 
                                         class="h-16 w-16 rounded-full border-2 border-white shadow-lg transition-transform duration-300 group-hover:scale-110">
                                @endif
                                <a href="{{ route('profile.edit') }}" 
                                   class="absolute bottom-0 right-0 bg-white rounded-full p-1 shadow-lg hover:bg-gray-100 transition-all duration-300 hover:scale-110">
                                    <svg class="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                              d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                              d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    </svg>
                                </a>
                            </div>
                            
                            <!-- User Info -->
                            <div class="sm:ml-4 mt-2 sm:mt-0 text-center sm:text-left">
                                <h1 class="text-xl font-bold text-gray-900 drop-shadow-lg">{{ Auth::user()->name }}</h1>
                                <p class="text-gray-800 text-sm font-medium">{{ '@' . Str::slug(Auth::user()->name) }}</p>
                                <div class="mt-1 flex items-center space-x-4 text-xs text-gray-700">
                                    <span class="flex items-center bg-white/30 px-2 py-1 rounded-full">
                                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                                  d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                        </svg>
                                        {{ Auth::user()->email }}
                                    </span>
                                    <span class="flex items-center bg-white/30 px-2 py-1 rounded-full">
                                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                                  d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                        </svg>
                                        Membre depuis {{ Auth::user()->created_at->format('F Y') }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Posts Section -->
            <div class="overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                     <h3 class="text-xl font-bold mb-6
                                    text-gray-800 dark:text-gray-100
                                    border-b-2 border-indigo-500 dark:border-indigo-400
                                    pb-3
                                    tracking-wide
                                    drop-shadow-sm
                                    uppercase
                                    ">
                            Vos publications
                     </h3>

                    <div class="space-y-6">
                        @foreach(Auth::user()->posts()->latest()->get() as $post)
                            <div class="p-6 rounded-2xl shadow-sm border
                            {{ $post->user_id === auth()->id() ? 'bg-white border-gray-200 dark:bg-gray-800 dark:border-gray-700' : 'bg-gray-50 border-gray-300 dark:bg-gray-900 dark:border-gray-700' }}">
                                <div class="flex items-center justify-between mb-4">
                                    
                                    <form action="{{ route('posts.destroy', $post) }}" method="POST">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="text-red-600 hover:text-red-900">
                                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                            </svg>
                                        </button>
                                    </form>
                                </div>

                                @if($post->content)
                                <p class="mb-4 text-gray-900 dark:text-gray-100">{{ $post->content }}</p>
                            @endif
                            
                            @if($post->image_path)
                                <div class="flex justify-center mb-4">
                                    <div class="max-w-full max-h-[24rem] w-full sm:w-[400px] rounded-lg overflow-hidden">
                                        <img src="{{ Storage::url($post->image_path) }}" 
                                             alt="Post image" 
                                             class="w-full h-full object-cover rounded-lg">
                                    </div>
                                </div>
                            @endif
                            
                            @if($post->video_path)
                                <div class="flex justify-center mb-4">
                                    <div class="max-w-full max-h-[24rem] w-full sm:w-[400px] rounded-lg overflow-hidden">
                                        <video controls class="w-full h-full rounded-lg object-cover">
                                            <source src="{{ Storage::url($post->video_path) }}" type="video/mp4">
                                            Votre navigateur ne supporte pas la lecture de vidéos.
                                        </video>
                                    </div>
                                </div>
                            @endif
                            

                                <div class="flex items-center justify-between">
                                    <div class="flex space-x-4">
                                        <form action="{{ route('posts.like', $post) }}" method="POST">
                                            @csrf
                                            @if($post->isLikedBy(auth()->user()))
                                                @method('DELETE')
                                                <button type="submit" class="flex items-center space-x-2 text-indigo-600">
                                                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                                        <path d="M2 10.5a1.5 1.5 0 113 0v6a1.5 1.5 0 01-3 0v-6zM6 10.333v5.43a2 2 0 001.106 1.79l.05.025A4 4 0 008.943 18h5.416a2 2 0 001.962-1.608l1.2-6A2 2 0 0015.56 8H12V4a2 2 0 00-2-2 1 1 0 00-1 1v.667a4 4 0 01-.8 2.4L6.8 7.933a4 4 0 00-.8 2.4z"></path>
                                                    </svg>
                                                    <span>{{ $post->likes->count() }}</span>
                                                </button>
                                            @else
                                                <button type="submit" class="flex items-center space-x-2 text-gray-600 hover:text-indigo-600">
                                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5"></path>
                                                    </svg>
                                                    <span>{{ $post->likes->count() }}</span>
                                                </button>
                                            @endif
                                        </form>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        @keyframes float {
            0% {
                transform: translateY(0) translateX(0);
                opacity: 0;
            }
            50% {
                opacity: 0.5;
            }
            100% {
                transform: translateY(-20px) translateX(10px);
                opacity: 0;
            }
        }
        .animate-float {
            animation: float 4s ease-in-out infinite;
        }
    </style>
</x-app-layout>
