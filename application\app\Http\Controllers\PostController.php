<?php

namespace App\Http\Controllers;

use App\Models\Post;
use App\Models\User;
use App\Models\Comment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;

class PostController extends Controller
{
   public function index()
{
    /** @var \App\Models\User $user */
    $user = Auth::user();

    // Forcer le retour en collection Laravel (plutôt qu’un array brut)
    $followingIds = $user->following()->pluck('following_id')->toBase();
    $followingIds->push($user->id);

    $posts = Post::with(['user', 'likes', 'comments.user'])
        ->whereIn('user_id', $followingIds)
        ->latest()
        ->paginate(10);

    return view('home', compact('posts'));
}

    public function store(Request $request)
    {
        $request->validate([
            'content' => 'required_without_all:image,video|string|nullable',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'video' => 'nullable|mimes:mp4,webm,ogg|max:20480'
        ]);

        $post = new Post();
        $post->user_id = Auth::id();
        $post->content = $request->content;

        if ($request->hasFile('image')) {
            try {
                $imagePath = storage_path('app/public/posts/images');
                if (!File::exists($imagePath)) {
                    File::makeDirectory($imagePath, 0755, true);
                }

                $file = $request->file('image');
                $filename = Str::random(40) . '.' . $file->getClientOriginalExtension();

                Storage::disk('public')->putFileAs('posts/images', $file, $filename);
                $post->image_path = 'posts/images/' . $filename;
            } catch (\Exception $e) {
                if ($request->wantsJson()) {
                    return response()->json(['success' => false, 'error' => 'Erreur téléchargement image: '.$e->getMessage()], 400);
                }
                return redirect()->back()->with('error', 'Erreur lors du téléchargement de l\'image: ' . $e->getMessage());
            }
        }

        if ($request->hasFile('video')) {
            try {
                $videoPath = storage_path('app/public/posts/videos');
                if (!File::exists($videoPath)) {
                    File::makeDirectory($videoPath, 0755, true);
                }

                $file = $request->file('video');
                $filename = Str::random(40) . '.' . $file->getClientOriginalExtension();

                Storage::disk('public')->putFileAs('posts/videos', $file, $filename);
                $post->video_path = 'posts/videos/' . $filename;
            } catch (\Exception $e) {
                if ($request->wantsJson()) {
                    return response()->json(['success' => false, 'error' => 'Erreur téléchargement vidéo: '.$e->getMessage()], 400);
                }
                return redirect()->back()->with('error', 'Erreur lors du téléchargement de la vidéo: ' . $e->getMessage());
            }
        }

        $post->save();

        if ($request->wantsJson()) {
            // recharger le post avec relations pour le front
            $post->load(['user', 'likes', 'comments']);
            return response()->json(['success' => true, 'post' => $post]);
        }

        return redirect()->back()->with('success', 'Post créé avec succès !');
    }

    public function destroy(Request $request, Post $post)
    {
        if ($post->user_id !== Auth::id()) {
            if ($request->wantsJson()) {
                return response()->json(['success' => false, 'error' => 'Action non autorisée'], 403);
            }
            return redirect()->back()->with('error', 'Action non autorisée');
        }

        if ($post->image_path) {
            Storage::disk('public')->delete($post->image_path);
        }

        if ($post->video_path) {
            Storage::disk('public')->delete($post->video_path);
        }

        $post->delete();

        if ($request->wantsJson()) {
            return response()->json(['success' => true]);
        }

        return redirect()->back()->with('success', 'Post supprimé avec succès.');
    }

    public function like(Request $request, Post $post)
    {
        $userId = Auth::id();

        if (!$post->likes()->where('user_id', $userId)->exists()) {
            $post->likes()->create(['user_id' => $userId]);
        }

        if ($request->wantsJson()) {
            return response()->json([
                'success' => true,
                'likes_count' => $post->likes()->count(),
            ]);
        }

        return redirect()->back();
    }

    public function unlike(Request $request, Post $post)
    {
        $userId = Auth::id();

        $post->likes()->where('user_id', $userId)->delete();

        if ($request->wantsJson()) {
            return response()->json([
                'success' => true,
                'likes_count' => $post->likes()->count(),
            ]);
        }

        return redirect()->back();
    }

    public function storeComment(Request $request, Post $post)
    {
        $request->validate([
            'content' => 'required|string|max:1000'
        ]);

        $comment = $post->comments()->create([
            'user_id' => Auth::id(),
            'content' => $request->content
        ]);

        if ($request->wantsJson()) {
            $comment->load('user');
            return response()->json(['success' => true, 'comment' => $comment]);
        }

        return redirect()->back()->with('success', 'Commentaire ajouté avec succès.');
    }

    public function destroyComment(Request $request, Post $post, Comment $comment)
    {
        if ($comment->user_id !== Auth::id()) {
            if ($request->wantsJson()) {
                return response()->json(['success' => false, 'error' => 'Action non autorisée'], 403);
            }
            return redirect()->back()->with('error', 'Action non autorisée');
        }

        $comment->delete();

        if ($request->wantsJson()) {
            return response()->json(['success' => true]);
        }

        return redirect()->back()->with('success', 'Commentaire supprimé avec succès.');
    }
}
