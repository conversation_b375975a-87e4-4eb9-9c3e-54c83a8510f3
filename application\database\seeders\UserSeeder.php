<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $users = [
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
            ],
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
            ],
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
            ],
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
            ],
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
            ],
            // 10 utilisateurs ajoutés
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
            ],
            [
                'name' => 'Youssef El Amrani',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
            ],
            [
                'name' => 'Chloe Nguyen',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
            ],
            [
                'name' => 'Mohamed Zaki',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
            ],
            [
                'name' => 'Lina Haddad',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
            ],
            [
                'name' => 'Ali Mansour',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
            ],
            [
                'name' => 'Sara Bouchra',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
            ],
            [
                'name' => 'David Brown',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
            ],
            [
                'name' => 'Fatima Zahra',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
            ],
            [
                'name' => 'Fatima Zahra',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
            ],
            [
                'name' => 'Omar Khalil',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
            ],
            [
                'name' => 'Omar Khalil',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
            ]
        ];

        foreach ($users as $userData) {
            User::create($userData);
        }

        // Créer des relations de follow aléatoires
        $allUsers = User::all();
        foreach ($allUsers as $user) {
            $usersToFollow = $allUsers->except($user->id)->random(rand(1, 3));
            foreach ($usersToFollow as $userToFollow) {
                $user->following()->attach($userToFollow->id);
            }
        }
    }
}
