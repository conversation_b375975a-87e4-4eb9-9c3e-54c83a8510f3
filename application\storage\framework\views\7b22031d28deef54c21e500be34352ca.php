<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="flex items-center space-x-4">
            <a href="<?php echo e(route('home')); ?>" class="text-indigo-600 hover:text-indigo-800 dark:text-indigo-400">
                ← Retour à l'accueil
            </a>
            <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                Post de <?php echo e($post->user->name); ?>

            </h2>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="py-12">
        <div class="max-w-3xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">

                <!-- Post Content -->
                <div class="p-6">
                    <!-- User Info -->
                    <div class="flex items-center space-x-3 mb-4">
                        <?php if($post->user->profile_image): ?>
                            <img class="h-12 w-12 rounded-full object-cover"
                                 src="<?php echo e(Storage::url($post->user->profile_image)); ?>"
                                 alt="<?php echo e($post->user->name); ?>">
                        <?php else: ?>
                            <img class="h-12 w-12 rounded-full"
                                 src="https://ui-avatars.com/api/?name=<?php echo e(urlencode($post->user->name)); ?>&size=48"
                                 alt="<?php echo e($post->user->name); ?>">
                        <?php endif; ?>

                        <div>
                            <h3 class="font-semibold text-gray-900 dark:text-gray-100">
                                <a href="<?php echo e(route('profile.show', $post->user)); ?>" class="hover:text-indigo-600 dark:hover:text-indigo-400">
                                    <?php echo e($post->user->name); ?>

                                </a>
                            </h3>
                            <p class="text-sm text-gray-500 dark:text-gray-400">
                                <?php echo e($post->created_at->diffForHumans()); ?>

                            </p>
                        </div>
                    </div>

                    <!-- Post Content -->
                    <?php if($post->content): ?>
                        <div class="mb-4">
                            <p class="text-gray-900 dark:text-gray-100 whitespace-pre-wrap"><?php echo e($post->content); ?></p>
                        </div>
                    <?php endif; ?>

                    <!-- Post Media -->
                    <?php if($post->image_path): ?>
                        <div class="post-media-container mb-4">
                            <img src="<?php echo e(Storage::url($post->image_path)); ?>"
                                 alt="Post image"
                                 class="post-image">
                        </div>
                    <?php endif; ?>

                    <?php if($post->video_path): ?>
                        <div class="post-media-container mb-4">
                            <video controls class="post-video">
                                <source src="<?php echo e(Storage::url($post->video_path)); ?>" type="video/mp4">
                                Votre navigateur ne supporte pas la lecture de vidéos.
                            </video>
                        </div>
                    <?php endif; ?>

                    <!-- Post Actions -->
                    <div class="flex items-center space-x-6 pt-4 border-t border-gray-200 dark:border-gray-700">
                        <!-- Like Button et Count séparés -->
                        <div class="flex items-center space-x-2">
                            <!-- Bouton Like seulement -->
                            <form action="<?php echo e($post->likes()->where('user_id', Auth::id())->exists() ? route('posts.unlike', $post) : route('posts.like', $post)); ?>"
                                  method="POST" class="inline">
                                <?php echo csrf_field(); ?>
                                <?php if($post->likes()->where('user_id', Auth::id())->exists()): ?>
                                    <?php echo method_field('DELETE'); ?>
                                <?php endif; ?>
                                <button type="submit" class="flex items-center <?php echo e($post->likes()->where('user_id', Auth::id())->exists() ? 'text-indigo-600' : 'text-gray-600 hover:text-indigo-600 dark:text-gray-400 dark:hover:text-indigo-400'); ?> transition-colors duration-200">
                                    <svg class="w-5 h-5" fill="<?php echo e($post->likes()->where('user_id', Auth::id())->exists() ? 'currentColor' : 'none'); ?>" stroke="currentColor" viewBox="0 0 24 24">
                                        <?php if($post->likes()->where('user_id', Auth::id())->exists()): ?>
                                            <path d="M2 10.5a1.5 1.5 0 113 0v6a1.5 1.5 0 01-3 0v-6zM6 10.333v5.43a2 2 0 001.106 1.79l.05.025A4 4 0 008.943 18h5.416a2 2 0 001.962-1.608l1.2-6A2 2 0 0015.56 8H12V4a2 2 0 00-2-2 1 1 0 00-1 1v.667a4 4 0 01-.8 2.4L6.8 7.933a4 4 0 00-.8 2.4z"></path>
                                        <?php else: ?>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                  d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5"></path>
                                        <?php endif; ?>
                                    </svg>
                                </button>
                            </form>

                            <!-- Nombre de likes séparé -->
                            <span onclick="showLikesModal(<?php echo e($post->id); ?>)"
                                  class="cursor-pointer hover:underline text-blue-600 font-semibold"><?php echo e($post->likes()->count()); ?></span>
                        </div>

                        <!-- Comment Count -->
                        <div class="flex items-center space-x-2 text-gray-500 dark:text-gray-400">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                            </svg>
                            <span><?php echo e($post->comments()->count()); ?></span>
                        </div>
                    </div>
                </div>

                <!-- Comments Section -->
                <div class="border-t border-gray-200 dark:border-gray-700">
                    <!-- Add Comment Form -->
                    <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                        <form action="<?php echo e(route('posts.comments.store', $post)); ?>" method="POST">
                            <?php echo csrf_field(); ?>
                            <div class="flex space-x-3">
                                <?php if(Auth::user()->profile_image): ?>
                                    <img class="h-8 w-8 rounded-full object-cover"
                                         src="<?php echo e(Storage::url(Auth::user()->profile_image)); ?>"
                                         alt="<?php echo e(Auth::user()->name); ?>">
                                <?php else: ?>
                                    <img class="h-8 w-8 rounded-full"
                                         src="https://ui-avatars.com/api/?name=<?php echo e(urlencode(Auth::user()->name)); ?>&size=32"
                                         alt="<?php echo e(Auth::user()->name); ?>">
                                <?php endif; ?>

                                <div class="flex-1">
                                    <textarea name="content"
                                              placeholder="Écrivez un commentaire..."
                                              class="w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-100 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500 resize-none"
                                              rows="2" required></textarea>
                                    <div class="mt-2">
                                        <button type="submit" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm transition">
                                            Commenter
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- Comments List -->
                    <div class="divide-y divide-gray-200 dark:divide-gray-700">
                        <?php $__empty_1 = true; $__currentLoopData = $post->comments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $comment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <div class="p-6">
                                <div class="flex space-x-3">
                                    <?php if($comment->user->profile_image): ?>
                                        <img class="h-8 w-8 rounded-full object-cover"
                                             src="<?php echo e(Storage::url($comment->user->profile_image)); ?>"
                                             alt="<?php echo e($comment->user->name); ?>">
                                    <?php else: ?>
                                        <img class="h-8 w-8 rounded-full"
                                             src="https://ui-avatars.com/api/?name=<?php echo e(urlencode($comment->user->name)); ?>&size=32"
                                             alt="<?php echo e($comment->user->name); ?>">
                                    <?php endif; ?>

                                    <div class="flex-1">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center space-x-2">
                                                <h4 class="font-medium text-gray-900 dark:text-gray-100">
                                                    <a href="<?php echo e(route('profile.show', $comment->user)); ?>" class="hover:text-indigo-600 dark:hover:text-indigo-400">
                                                        <?php echo e($comment->user->name); ?>

                                                    </a>
                                                </h4>
                                                <span class="text-sm text-gray-500 dark:text-gray-400">
                                                    <?php echo e($comment->created_at->diffForHumans()); ?>

                                                </span>
                                            </div>

                                            <?php if(auth()->check() && $comment->user_id === auth()->id()): ?>
                                                <div class="relative">
                                                    <button onclick="toggleCommentMenu(<?php echo e($comment->id); ?>)"
                                                        class="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-full hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
                                                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                                            <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"></path>
                                                        </svg>
                                                    </button>

                                                    <!-- Dropdown Menu -->
                                                    <div id="comment-menu-<?php echo e($comment->id); ?>" class="hidden absolute right-0 mt-1 w-32 bg-white dark:bg-gray-800 rounded-md shadow-lg border border-gray-200 dark:border-gray-700 z-10">
                                                        <button type="button"
                                                            onclick="deleteComment(<?php echo e($comment->id); ?>)"
                                                            class="w-full text-left px-3 py-2 text-sm text-red-600 hover:bg-red-50 dark:text-red-400 dark:hover:bg-red-900 rounded-md transition-colors">
                                                            <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                            </svg>
                                                            Supprimer
                                                        </button>
                                                    </div>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        <p class="mt-1 text-gray-700 dark:text-gray-300"><?php echo e($comment->content); ?></p>

                                        <?php if(auth()->guard()->check()): ?>
                                        <!-- Actions du commentaire -->
                                        <div class="flex items-center space-x-4 mt-3">
                                            <!-- Bouton Like -->
                                            <button type="button"
                                                onclick="toggleCommentLike(<?php echo e($comment->id); ?>)"
                                                id="comment-like-btn-<?php echo e($comment->id); ?>"
                                                class="flex items-center space-x-1 text-sm <?php echo e($comment->isLikedBy(auth()->user()) ? 'text-indigo-600' : 'text-gray-500 hover:text-indigo-600'); ?> transition-colors duration-200">
                                                <svg id="comment-like-icon-<?php echo e($comment->id); ?>" class="w-4 h-4"
                                                     fill="<?php echo e($comment->isLikedBy(auth()->user()) ? 'currentColor' : 'none'); ?>"
                                                     stroke="currentColor" viewBox="0 0 24 24">
                                                    <?php if($comment->isLikedBy(auth()->user())): ?>
                                                        <path d="M14 9V5a3 3 0 0 0-3-3l-4 9v11h11.28a2 2 0 0 0 2-1.7l1.38-9a2 2 0 0 0-2-2.3zM7 22H4a2 2 0 0 1-2-2v-7a2 2 0 0 1 2-2h3"></path>
                                                    <?php else: ?>
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.60L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5"></path>
                                                    <?php endif; ?>
                                                </svg>
                                                <span id="comment-like-count-<?php echo e($comment->id); ?>"><?php echo e($comment->likes()->count()); ?></span>
                                            </button>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <div class="p-6 text-center text-gray-500 dark:text-gray-400">
                                <p>Aucun commentaire pour le moment.</p>
                                <p class="text-sm">Soyez le premier à commenter !</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal pour afficher les likes -->
    <div id="likes-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
        <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl max-w-md w-full max-h-[80vh] overflow-hidden">
            <!-- Header du modal -->
            <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                    Personnes qui ont aimé
                </h3>
                <button onclick="closeLikesModal()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <!-- Contenu du modal -->
            <div class="p-6">
                <!-- Loading state -->
                <div id="likes-loading" class="flex items-center justify-center py-8">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
                    <span class="ml-3 text-gray-600 dark:text-gray-400">Chargement...</span>
                </div>

                <!-- Liste des likes -->
                <div id="likes-list" class="hidden space-y-3 max-h-96 overflow-y-auto">
                    <!-- Les likes seront ajoutés ici dynamiquement -->
                </div>

                <!-- État vide -->
                <div id="likes-empty" class="hidden text-center py-8">
                    <svg class="w-16 h-16 mx-auto text-gray-400 dark:text-gray-500 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5"></path>
                    </svg>
                    <p class="text-gray-500 dark:text-gray-400">Aucun like pour le moment</p>
                </div>
            </div>
        </div>
    </div>

    <script>
    // Fonctions pour gérer le modal des likes
    async function showLikesModal(postId) {
        const modal = document.getElementById('likes-modal');
        const loading = document.getElementById('likes-loading');
        const likesList = document.getElementById('likes-list');
        const likesEmpty = document.getElementById('likes-empty');

        // Afficher le modal
        modal.classList.remove('hidden');
        document.body.style.overflow = 'hidden';

        // Réinitialiser l'état
        loading.classList.remove('hidden');
        likesList.classList.add('hidden');
        likesEmpty.classList.add('hidden');
        likesList.innerHTML = '';

        try {
            const response = await axios.get(`/posts/${postId}/likes`, {
                headers: {
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            if (response.data.success) {
                const likes = response.data.likes;

                loading.classList.add('hidden');

                if (likes.length === 0) {
                    likesEmpty.classList.remove('hidden');
                } else {
                    likesList.classList.remove('hidden');

                    likes.forEach(like => {
                        const likeElement = createLikeElement(like);
                        likesList.appendChild(likeElement);
                    });
                }
            }
        } catch (error) {
            console.error('Erreur lors du chargement des likes:', error);
            loading.classList.add('hidden');

            likesList.innerHTML = `
                <div class="text-center py-8">
                    <p class="text-red-500">Erreur lors du chargement des likes</p>
                    <button onclick="showLikesModal(${postId})" class="mt-2 text-indigo-600 hover:text-indigo-800">
                        Réessayer
                    </button>
                </div>
            `;
            likesList.classList.remove('hidden');
        }
    }

    function closeLikesModal() {
        const modal = document.getElementById('likes-modal');
        modal.classList.add('hidden');
        document.body.style.overflow = 'auto';
    }

    function createLikeElement(like) {
        const div = document.createElement('div');
        div.className = 'flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors';

        const profileImage = like.profile_image_url
            ? like.profile_image_url
            : `https://ui-avatars.com/api/?name=${encodeURIComponent(like.name)}&size=40`;

        div.innerHTML = `
            <div class="flex items-center space-x-3">
                <a href="/profile/${like.id}" class="flex-shrink-0">
                    <img class="h-10 w-10 rounded-full object-cover border-2 border-gray-200 dark:border-gray-600"
                         src="${profileImage}"
                         alt="${like.name}">
                </a>
                <div>
                    <a href="/profile/${like.id}" class="font-medium text-gray-900 dark:text-gray-100 hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors">
                        ${like.name}
                    </a>
                    <p class="text-sm text-gray-500 dark:text-gray-400">${like.liked_at}</p>
                </div>
            </div>
            <div class="flex items-center space-x-2">
                <svg class="w-5 h-5 text-indigo-600" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2 10.5a1.5 1.5 0 113 0v6a1.5 1.5 0 01-3 0v-6zM6 10.333v5.43a2 2 0 001.106 1.79l.05.025A4 4 0 008.943 18h5.416a2 2 0 001.962-1.608l1.2-6A2 2 0 0015.56 8H12V4a2 2 0 00-2-2 1 1 0 00-1 1v.667a4 4 0 01-.8 2.4L6.8 7.933a4 4 0 00-.8 2.4z"></path>
                </svg>
            </div>
        `;

        return div;
    }

    // Fermer le modal en cliquant à l'extérieur
    document.getElementById('likes-modal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeLikesModal();
        }
    });

    // Fonction pour supprimer un commentaire
    async function deleteComment(commentId) {
        // Demander confirmation
        if (!confirm('Êtes-vous sûr de vouloir supprimer ce commentaire ? Cette action est irréversible.')) {
            return;
        }

        try {
            const response = await fetch(`/comments/${commentId}`, {
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            if (data.success) {
                // Trouver l'élément du commentaire (dans posts.show, c'est le div parent)
                const commentElement = document.querySelector(`#comment-menu-${commentId}`).closest('.p-6');

                if (commentElement) {
                    // Animation de disparition
                    commentElement.style.transition = 'all 0.3s ease-out';
                    commentElement.style.opacity = '0';
                    commentElement.style.transform = 'translateX(-20px)';

                    setTimeout(() => {
                        commentElement.remove();
                    }, 300);
                }

                // Afficher une notification de succès (si vous avez un système de notifications)
                alert(data.message);
            }
        } catch (error) {
            console.error('Erreur lors de la suppression du commentaire:', error);
            alert('Erreur lors de la suppression');
        }
    }

    // Fonction pour toggle le menu des commentaires
    function toggleCommentMenu(commentId) {
        const menu = document.getElementById(`comment-menu-${commentId}`);
        const allMenus = document.querySelectorAll('[id^="comment-menu-"]');

        // Fermer tous les autres menus
        allMenus.forEach(otherMenu => {
            if (otherMenu.id !== `comment-menu-${commentId}`) {
                otherMenu.classList.add('hidden');
            }
        });

        // Toggle ce menu
        menu.classList.toggle('hidden');
    }

    // Fonction pour toggle le like d'un commentaire
    async function toggleCommentLike(commentId) {
        const likeBtn = document.getElementById(`comment-like-btn-${commentId}`);
        const likeIcon = document.getElementById(`comment-like-icon-${commentId}`);
        const likeCount = document.getElementById(`comment-like-count-${commentId}`);

        // Désactiver temporairement le bouton
        likeBtn.disabled = true;

        try {
            const response = await fetch(`/comments/${commentId}/like`, {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            if (data.success) {
                // Mettre à jour l'interface
                likeCount.textContent = data.likes_count;

                if (data.liked) {
                    // L'utilisateur a liké
                    likeBtn.classList.remove('text-gray-500', 'hover:text-indigo-600');
                    likeBtn.classList.add('text-indigo-600');
                    likeIcon.setAttribute('fill', 'currentColor');
                    likeIcon.innerHTML = '<path d="M14 9V5a3 3 0 0 0-3-3l-4 9v11h11.28a2 2 0 0 0 2-1.7l1.38-9a2 2 0 0 0-2-2.3zM7 22H4a2 2 0 0 1-2-2v-7a2 2 0 0 1 2-2h3"></path>';
                } else {
                    // L'utilisateur a unliké
                    likeBtn.classList.remove('text-indigo-600');
                    likeBtn.classList.add('text-gray-500', 'hover:text-indigo-600');
                    likeIcon.setAttribute('fill', 'none');
                    likeIcon.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.60L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5"></path>';
                }

                // Animation du bouton
                likeBtn.style.transform = 'scale(1.1)';
                setTimeout(() => {
                    likeBtn.style.transform = 'scale(1)';
                }, 150);
            }
        } catch (error) {
            console.error('Erreur lors du toggle du like:', error);
            alert('Erreur lors de l\'action');
        } finally {
            // Réactiver le bouton
            likeBtn.disabled = false;
        }
    }

    // Fermer les menus quand on clique ailleurs
    document.addEventListener('click', function(event) {
        if (!event.target.closest('[onclick*="toggleCommentMenu"]') && !event.target.closest('[id^="comment-menu-"]')) {
            const allMenus = document.querySelectorAll('[id^="comment-menu-"]');
            allMenus.forEach(menu => menu.classList.add('hidden'));
        }
    });

    // Fermer le modal avec la touche Escape
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            const modal = document.getElementById('likes-modal');
            if (!modal.classList.contains('hidden')) {
                closeLikesModal();
            }
        }
    });
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Desktop\euromedconnect\application\resources\views/posts/show.blade.php ENDPATH**/ ?>